import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import subprocess
import threading
from pathlib import Path
import json

class VideoCompressor:
    def __init__(self, root):
        self.root = root
        self.root.title("视频压缩工具 - AMD显卡加速")
        self.root.geometry("800x600")

        # 加载配置
        self.config = self.load_config()

        # 视频文件列表
        self.video_files = []

        # 创建界面
        self.create_widgets()

        # 检查FFmpeg是否可用
        self.check_ffmpeg()

    def load_config(self):
        """加载配置文件"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            # 如果配置文件不存在，返回默认配置
            return {
                "default_settings": {
                    "quality": "medium",
                    "resolution": "原始",
                    "use_gpu": True,
                    "output_folder_name": "compressed"
                },
                "supported_formats": ["*.mp4", "*.avi", "*.mov", "*.mkv", "*.wmv", "*.flv", "*.webm", "*.m4v"],
                "resolution_options": ["原始", "1920x1080", "1280x720", "854x480"]
            }

    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="视频压缩工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)

        ttk.Button(file_frame, text="选择视频文件", command=self.select_files).grid(row=0, column=0, padx=(0, 10))

        self.file_count_label = ttk.Label(file_frame, text="未选择文件")
        self.file_count_label.grid(row=0, column=1, sticky=tk.W)

        # 文件列表
        list_frame = ttk.Frame(file_frame)
        list_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        list_frame.columnconfigure(0, weight=1)

        # 创建Treeview来显示文件列表
        self.file_tree = ttk.Treeview(list_frame, columns=("size", "status"), show="tree headings", height=8)
        self.file_tree.heading("#0", text="文件名")
        self.file_tree.heading("size", text="大小")
        self.file_tree.heading("status", text="状态")

        self.file_tree.column("#0", width=400)
        self.file_tree.column("size", width=100)
        self.file_tree.column("status", width=100)

        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        self.file_tree.configure(yscrollcommand=scrollbar.set)

        self.file_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 压缩设置区域
        settings_frame = ttk.LabelFrame(main_frame, text="压缩设置", padding="10")
        settings_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # 质量设置
        ttk.Label(settings_frame, text="压缩质量:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.quality_var = tk.StringVar(value=self.config["default_settings"]["quality"])
        quality_combo = ttk.Combobox(settings_frame, textvariable=self.quality_var,
                                   values=["high", "medium", "low"], state="readonly", width=15)
        quality_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        # 分辨率设置
        ttk.Label(settings_frame, text="目标分辨率:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        self.resolution_var = tk.StringVar(value=self.config["default_settings"]["resolution"])
        resolution_combo = ttk.Combobox(settings_frame, textvariable=self.resolution_var,
                                      values=self.config.get("resolution_options", ["原始", "1920x1080", "1280x720", "854x480"]),
                                      state="readonly", width=15)
        resolution_combo.grid(row=0, column=3, sticky=tk.W)

        # 硬件加速选项
        self.use_gpu_var = tk.BooleanVar(value=self.config["default_settings"]["use_gpu"])
        gpu_check = ttk.Checkbutton(settings_frame, text="使用AMD显卡硬件加速", variable=self.use_gpu_var)
        gpu_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(10, 0))

        # 进度区域
        progress_frame = ttk.LabelFrame(main_frame, text="压缩进度", padding="10")
        progress_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        self.status_label = ttk.Label(progress_frame, text="准备就绪")
        self.status_label.grid(row=1, column=0, sticky=tk.W)

        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=(10, 0))

        self.start_button = ttk.Button(button_frame, text="开始压缩", command=self.start_compression)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(button_frame, text="停止", command=self.stop_compression, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="清空列表", command=self.clear_files).pack(side=tk.LEFT)

        # 压缩控制变量
        self.is_compressing = False
        self.compression_thread = None

    def check_ffmpeg(self):
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
            if result.returncode == 0:
                self.status_label.config(text="FFmpeg已就绪")
            else:
                self.status_label.config(text="FFmpeg不可用，请安装FFmpeg")
        except FileNotFoundError:
            self.status_label.config(text="未找到FFmpeg，请安装FFmpeg")
            messagebox.showwarning("警告", "未找到FFmpeg。请下载并安装FFmpeg，然后将其添加到系统PATH中。")

    def select_files(self):
        """选择视频文件"""
        supported_formats = self.config.get("supported_formats", ["*.mp4", "*.avi", "*.mov", "*.mkv", "*.wmv", "*.flv", "*.webm", "*.m4v"])
        file_types = [
            ("视频文件", " ".join(supported_formats)),
            ("所有文件", "*.*")
        ]

        files = filedialog.askopenfilenames(
            title="选择视频文件",
            filetypes=file_types
        )

        if files:
            self.video_files.extend(files)
            self.update_file_list()

    def update_file_list(self):
        """更新文件列表显示"""
        # 清空现有列表
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        # 添加文件到列表
        for file_path in self.video_files:
            file_name = os.path.basename(file_path)
            try:
                file_size = os.path.getsize(file_path)
                size_mb = f"{file_size / (1024*1024):.1f} MB"
            except:
                size_mb = "未知"

            self.file_tree.insert("", tk.END, text=file_name, values=(size_mb, "等待"))

        # 更新文件计数
        count = len(self.video_files)
        self.file_count_label.config(text=f"已选择 {count} 个文件")

    def clear_files(self):
        """清空文件列表"""
        if self.is_compressing:
            messagebox.showwarning("警告", "正在压缩中，无法清空列表")
            return

        self.video_files.clear()
        self.update_file_list()

    def get_ffmpeg_command(self, input_file, output_file):
        """生成FFmpeg命令"""
        cmd = ['ffmpeg', '-i', input_file]

        # 硬件加速设置（AMD）
        if self.use_gpu_var.get():
            # 使用AMD AMF编码器
            cmd.extend(['-c:v', 'h264_amf'])
        else:
            # 使用软件编码
            cmd.extend(['-c:v', 'libx264'])

        # 质量设置
        quality = self.quality_var.get()
        if quality == "high":
            if self.use_gpu_var.get():
                cmd.extend(['-quality', 'quality'])
            else:
                cmd.extend(['-crf', '18'])
        elif quality == "medium":
            if self.use_gpu_var.get():
                cmd.extend(['-quality', 'balanced'])
            else:
                cmd.extend(['-crf', '23'])
        else:  # low
            if self.use_gpu_var.get():
                cmd.extend(['-quality', 'speed'])
            else:
                cmd.extend(['-crf', '28'])

        # 分辨率设置
        resolution = self.resolution_var.get()
        if resolution != "原始":
            cmd.extend(['-vf', f'scale={resolution}'])

        # 音频编码
        cmd.extend(['-c:a', 'aac', '-b:a', '128k'])

        # 输出文件
        cmd.extend(['-y', output_file])  # -y 覆盖输出文件

        return cmd

    def create_output_folder(self, input_file):
        """在原始文件夹下创建压缩文件夹"""
        input_path = Path(input_file)
        parent_dir = input_path.parent
        folder_name = self.config["default_settings"].get("output_folder_name", "compressed")
        output_dir = parent_dir / folder_name

        # 创建输出文件夹
        output_dir.mkdir(exist_ok=True)

        # 生成输出文件名
        output_filename = f"compressed_{input_path.stem}.mp4"
        output_path = output_dir / output_filename

        return str(output_path)

    def update_file_status(self, file_index, status):
        """更新文件状态"""
        items = self.file_tree.get_children()
        if file_index < len(items):
            item = items[file_index]
            current_values = list(self.file_tree.item(item, "values"))
            current_values[1] = status  # 状态列
            self.file_tree.item(item, values=current_values)

    def start_compression(self):
        """开始压缩"""
        if not self.video_files:
            messagebox.showwarning("警告", "请先选择视频文件")
            return

        if self.is_compressing:
            messagebox.showwarning("警告", "正在压缩中，请等待完成")
            return

        # 启动压缩线程
        self.is_compressing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)

        self.compression_thread = threading.Thread(target=self.compress_videos, daemon=True)
        self.compression_thread.start()

    def stop_compression(self):
        """停止压缩"""
        self.is_compressing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_label.config(text="压缩已停止")

    def compress_videos(self):
        """压缩视频的主要逻辑"""
        total_files = len(self.video_files)
        successful = 0
        failed = 0

        for i, input_file in enumerate(self.video_files):
            if not self.is_compressing:
                break

            try:
                # 更新状态
                self.root.after(0, lambda idx=i: self.update_file_status(idx, "压缩中"))
                self.root.after(0, lambda: self.status_label.config(text=f"正在压缩: {os.path.basename(input_file)}"))

                # 创建输出文件路径
                output_file = self.create_output_folder(input_file)

                # 生成FFmpeg命令
                cmd = self.get_ffmpeg_command(input_file, output_file)

                # 执行压缩
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

                # 等待完成
                stdout, stderr = process.communicate()

                if process.returncode == 0:
                    self.root.after(0, lambda idx=i: self.update_file_status(idx, "完成"))
                    successful += 1
                else:
                    self.root.after(0, lambda idx=i: self.update_file_status(idx, "失败"))
                    failed += 1
                    print(f"压缩失败: {input_file}")
                    print(f"错误信息: {stderr}")

            except Exception as e:
                self.root.after(0, lambda idx=i: self.update_file_status(idx, "错误"))
                failed += 1
                print(f"处理文件时出错: {input_file}, 错误: {str(e)}")

            # 更新进度
            progress = ((i + 1) / total_files) * 100
            self.root.after(0, lambda p=progress: self.progress_var.set(p))

        # 压缩完成
        self.is_compressing = False
        self.root.after(0, lambda: self.start_button.config(state=tk.NORMAL))
        self.root.after(0, lambda: self.stop_button.config(state=tk.DISABLED))

        if self.is_compressing:  # 如果没有被手动停止
            self.root.after(0, lambda: self.status_label.config(text=f"压缩完成! 成功: {successful}, 失败: {failed}"))
            self.root.after(0, lambda: messagebox.showinfo("完成", f"压缩完成!\n成功: {successful} 个文件\n失败: {failed} 个文件"))


def main():
    root = tk.Tk()
    app = VideoCompressor(root)
    root.mainloop()


if __name__ == "__main__":
    main()