#!/usr/bin/env python3
"""
FFmpeg安装检查工具
检查FFmpeg是否正确安装并支持AMD硬件加速
"""

import subprocess
import sys

def check_ffmpeg():
    """检查FFmpeg是否安装"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FFmpeg已安装")
            version_line = result.stdout.split('\n')[0]
            print(f"版本信息: {version_line}")
            return True
        else:
            print("❌ FFmpeg安装有问题")
            return False
    except FileNotFoundError:
        print("❌ 未找到FFmpeg")
        print("请从 https://ffmpeg.org/download.html 下载并安装FFmpeg")
        print("安装后需要将FFmpeg添加到系统PATH环境变量中")
        return False

def check_amd_support():
    """检查AMD硬件加速支持"""
    try:
        result = subprocess.run(['ffmpeg', '-encoders'], capture_output=True, text=True)
        if result.returncode == 0:
            if 'h264_amf' in result.stdout:
                print("✅ 支持AMD硬件加速 (h264_amf)")
                return True
            else:
                print("⚠️  不支持AMD硬件加速")
                print("可能原因：")
                print("- AMD显卡驱动程序过旧")
                print("- FFmpeg版本不支持AMF")
                print("- 显卡不支持硬件编码")
                return False
        else:
            print("❌ 无法检查编码器支持")
            return False
    except Exception as e:
        print(f"❌ 检查AMD支持时出错: {e}")
        return False

def main():
    print("=== FFmpeg安装检查 ===")
    print()
    
    ffmpeg_ok = check_ffmpeg()
    print()
    
    if ffmpeg_ok:
        amd_ok = check_amd_support()
        print()
        
        if amd_ok:
            print("🎉 系统已准备就绪！可以使用AMD硬件加速进行视频压缩。")
        else:
            print("⚠️  可以使用软件编码进行视频压缩，但速度较慢。")
    else:
        print("❌ 请先安装FFmpeg后再运行视频压缩工具。")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    main()
