视频压缩工具使用说明
==================

这个工具专为微信小程序视频上传而设计，支持AMD显卡硬件加速，可以快速压缩多个视频文件。

快速开始：
1. 双击 run.bat 启动程序
2. 或者在命令行运行：python main.py

首次使用：
1. 运行 python check_ffmpeg.py 检查FFmpeg是否安装
2. 如果未安装，请参考 INSTALL.md 安装FFmpeg

主要功能：
✓ 批量选择多个视频文件
✓ AMD显卡硬件加速（RX550等）
✓ 自动在原文件夹创建"compressed"子文件夹
✓ 支持多种视频格式（MP4、AVI、MOV等）
✓ 可调节压缩质量和分辨率
✓ 实时显示压缩进度

使用步骤：
1. 点击"选择视频文件"按钮
2. 选择要压缩的视频文件（可多选）
3. 设置压缩参数：
   - 压缩质量：high（高质量）、medium（推荐）、low（快速）
   - 目标分辨率：建议选择1280x720或854x480
   - 硬件加速：建议保持开启
4. 点击"开始压缩"
5. 等待压缩完成

输出说明：
- 压缩后的文件保存在原文件夹的"compressed"子文件夹中
- 文件名格式：compressed_原始文件名.mp4
- 输出格式：MP4（H.264编码，AAC音频）

注意事项：
- 确保有足够的磁盘空间
- 压缩过程中不要关闭程序
- 如果硬件加速不可用，会自动使用软件编码
- 建议关闭其他占用GPU的程序以获得最佳性能

故障排除：
- 如果提示FFmpeg未找到，请安装FFmpeg并添加到PATH
- 如果压缩失败，检查原文件是否损坏
- 如果速度很慢，确认是否启用了硬件加速

技术支持：
- 查看 README.md 了解详细信息
- 查看 INSTALL.md 了解安装步骤
- 运行 check_ffmpeg.py 检查系统环境
