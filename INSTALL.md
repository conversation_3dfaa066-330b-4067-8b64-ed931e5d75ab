# 安装指南

## 快速开始

1. **检查系统环境**
   ```bash
   python check_ffmpeg.py
   ```

2. **如果FFmpeg未安装，请按照下面的步骤安装**

3. **运行程序**
   ```bash
   python main.py
   ```
   或者双击 `run.bat` 文件

## 详细安装步骤

### 1. 安装FFmpeg

#### 方法一：使用预编译版本（推荐）

1. 访问 [FFmpeg官网](https://ffmpeg.org/download.html)
2. 点击 "Windows" 下的 "Windows builds by BtbN"
3. 下载最新的 `ffmpeg-master-latest-win64-gpl.zip`
4. 解压到 `C:\ffmpeg` 目录
5. 添加到PATH环境变量：
   - 打开"系统属性" → "高级" → "环境变量"
   - 在"系统变量"中找到"Path"，点击"编辑"
   - 点击"新建"，添加 `C:\ffmpeg\bin`
   - 点击"确定"保存

#### 方法二：使用包管理器

如果你安装了Chocolatey：
```bash
choco install ffmpeg
```

如果你安装了Scoop：
```bash
scoop install ffmpeg
```

### 2. 验证安装

打开新的命令提示符窗口，输入：
```bash
ffmpeg -version
```

如果显示版本信息，说明安装成功。

### 3. 检查AMD硬件加速支持

运行检查工具：
```bash
python check_ffmpeg.py
```

如果显示"支持AMD硬件加速"，说明可以使用显卡加速。

## 常见问题

### Q: 提示"未找到FFmpeg"
A: 
- 确保FFmpeg已正确安装
- 检查PATH环境变量是否正确设置
- 重启命令提示符或重新登录Windows

### Q: 不支持AMD硬件加速
A:
- 更新AMD显卡驱动程序
- 确保使用的是支持AMF的FFmpeg版本
- 可以取消勾选硬件加速选项，使用软件编码

### Q: 压缩速度很慢
A:
- 确保启用了AMD硬件加速
- 选择较低的压缩质量设置
- 关闭其他占用GPU的程序

### Q: 压缩后文件很大
A:
- 选择较低的分辨率
- 使用"low"质量设置
- 检查原始文件是否已经是压缩格式

## 系统要求

- **操作系统**: Windows 10/11
- **Python**: 3.7或更高版本
- **显卡**: AMD RX系列或更新（支持AMF编码器）
- **内存**: 建议4GB以上
- **存储**: 确保有足够空间存储压缩后的文件

## 性能优化建议

1. **使用SSD存储**：提高文件读写速度
2. **关闭不必要程序**：释放GPU和CPU资源
3. **批量处理**：一次处理多个文件更高效
4. **合适的设置**：根据需求选择质量和分辨率

## 技术支持

如果遇到问题，请：
1. 运行 `python check_ffmpeg.py` 检查环境
2. 查看控制台输出的错误信息
3. 确认文件格式是否支持
4. 检查磁盘空间是否充足
