# 视频压缩工具

一个支持AMD显卡硬件加速的视频批量压缩工具，专为微信小程序视频上传优化。

## 功能特点

- 🚀 **AMD显卡硬件加速**：支持AMD RX550等显卡的AMF编码器
- 📁 **批量处理**：可同时选择多个视频文件进行压缩
- 🎯 **智能输出**：在原始文件夹下自动创建"compressed"文件夹
- ⚙️ **灵活设置**：支持多种压缩质量和分辨率选项
- 📊 **实时进度**：显示压缩进度和文件状态
- 🎨 **友好界面**：简洁易用的图形界面

## 系统要求

- Windows 10/11
- Python 3.7+
- AMD显卡（支持AMF编码器）
- FFmpeg

## 安装步骤

### 1. 安装FFmpeg

1. 访问 [FFmpeg官网](https://ffmpeg.org/download.html)
2. 下载Windows版本的FFmpeg
3. 解压到任意目录（如 `C:\ffmpeg`）
4. 将FFmpeg的bin目录添加到系统PATH环境变量
5. 打开命令提示符，输入 `ffmpeg -version` 验证安装

### 2. 运行程序

```bash
python main.py
```

## 使用方法

1. **选择文件**：点击"选择视频文件"按钮，选择要压缩的视频文件
2. **设置参数**：
   - 压缩质量：high（高质量）、medium（中等）、low（快速）
   - 目标分辨率：原始、1920x1080、1280x720、854x480
   - 硬件加速：建议保持开启以获得更快的压缩速度
3. **开始压缩**：点击"开始压缩"按钮
4. **查看结果**：压缩完成的文件将保存在原始文件夹的"compressed"子文件夹中

## 输出文件说明

- 输出格式：MP4（H.264编码）
- 音频编码：AAC 128kbps
- 文件命名：`compressed_原始文件名.mp4`
- 保存位置：原始文件夹/compressed/

## 支持的视频格式

输入格式：MP4、AVI、MOV、MKV、WMV、FLV、WebM、M4V等

## 故障排除

### FFmpeg未找到
- 确保FFmpeg已正确安装并添加到PATH环境变量
- 重启程序或重新打开命令提示符

### AMD硬件加速不可用
- 确保显卡驱动程序是最新版本
- 检查显卡是否支持AMF编码器
- 可以取消勾选"使用AMD显卡硬件加速"使用软件编码

### 压缩失败
- 检查输入文件是否损坏
- 确保有足够的磁盘空间
- 查看控制台输出的错误信息

## 技术说明

- 使用FFmpeg的h264_amf编码器进行AMD硬件加速
- 多线程处理，界面响应流畅
- 自动创建输出文件夹，避免文件混乱

## 许可证

MIT License
